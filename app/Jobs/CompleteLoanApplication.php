<?php

namespace App\Jobs;

use App\Exceptions\MtnApiException;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\MtnApiService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class CompleteLoanApplication implements ShouldQueue
{
    use Queueable;

    protected MtnApiService $mtnApiService;

    public function __construct(protected Transaction $transaction)
    {
        $this->mtnApiService = new MtnApiService('test');
    }

    /**
     * @throws MtnApiException
     * @throws \Exception
     */
    public function handle(): void
    {
        $loanCompleted = $this->mtnApiService->loanApplicationCompleted($this->transaction);

        if (! $loanCompleted) {
            // todo: Send an SMS to Customer about this failure.
            throw new MtnApiException('Acknowledgement not received from MTN');
        }

        // todo: Send an SMS to Agent
        // todo: Disburse funds
        $this->mtnApiService->disburse($this->transaction->Telephone_Number, $this->transaction->Amount, $this->transaction->TXN_ID);
    }
}
