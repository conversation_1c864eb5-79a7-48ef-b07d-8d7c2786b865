<?php

namespace App\Jobs;

use App\Exceptions\MtnApiException;
use App\Models\Customer;
use App\Services\MtnApiService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class CompleteCustomerRegistration implements ShouldQueue
{
    use Queueable;

    protected MtnApiService $mtnApiService;

    public function __construct(public Customer $customer)
    {
        $this->mtnApiService = new MtnApiService('test');
    }

    /**
     * @throws MtnApiException
     * @throws ConnectionException
     * @throws GuzzleException
     */
    public function handle(): void
    {
        $registrationCompleted = $this->mtnApiService->customerRegistrationCompleted($this->customer);

        if (! $registrationCompleted) {
            throw new MtnApiException('Registration failed');
        }

        // todo: Send an SMS to customer
    }
}
