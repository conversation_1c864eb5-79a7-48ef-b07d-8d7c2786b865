<?php

namespace App\Services\Factories;

use App\Services\Contracts\ProvidesTransactableAPIs;
use App\Services\YoPaymentApiService;
use App\Services\AirtelOpenApiService;
use App\Services\MtnApiService;
use App\Exceptions\PaymentServiceException;
use App\Services\MockMtnApiService;

class PaymentServiceFactory
{
    public static function create(string $provider, string $environment, array $config): ProvidesTransactableAPIs
    {
        try {
            return match($provider) {
                'yo' => new YoPaymentApiService($environment, $config),
                'airtel' => new AirtelOpenApiService($environment, $config),
                'mtn' => new MtnApiService($environment, $config),
                default => throw new PaymentServiceException('Unknown payment gateway')
            };
        } catch (\Exception $e) {
            throw new PaymentServiceException(
                "Failed to create payment service: {$e->getMessage()}"
            );
        }
    }
}
