<?php

namespace App\Services;

use App\Jobs\SendSMSNotification;
use App\Models\Loan;
use Carbon\Carbon;
use App\Models\SmsLog;
use App\Models\LoanSchedule;
use App\Notifications\SmsNotification;
use Illuminate\Support\Facades\Log;

class LoanRepaymentReminderService
{
    public function sendReminders()
    {
        $loans = Loan::where('Credit_Account_Status', Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS)
            ->whereHas('loan_product.smsTemplates')
            ->orderBy('id', 'desc')
            ->get();
        foreach ($loans as $loan) {
            $schedules = $loan->schedule()
                ->where('total_outstanding', '>', 0)
                ->orderBy('installment_number', 'asc')
                ->get()
                ->groupBy('installment_number')
                ->first();

            if (!$schedules) continue;
            $totalAmount = $schedules->sum('total_outstanding');
            $paymentDate = $schedules->first()->payment_due_date;

            $customer = $loan->customer;
            if (!$loan->loan_product->smsTemplates) continue;

            foreach ($loan->loan_product->smsTemplates as $template) {
                $smsDate = Carbon::parse($paymentDate)
                    ->addDays((int)$template->Day)
                    ->format('Y-m-d');
                if ($smsDate !== now()->format('Y-m-d')) continue;
                try {
                    $smsText = str_replace(
                        [':Amount', ':Partner', ':Date'],
                        [
                            number_format($totalAmount),
                            $loan->partner->Institution_Name,
                            $paymentDate->format('Y-m-d')
                        ],
                        $template->Template
                    );
                    $customer->notify(new SmsNotification(
                        $smsText,
                        $customer->Telephone_Number,
                        $customer->id,
                        $loan->Partner_ID
                    ));
                } catch (\Throwable $e) {
                    Log::error('SMS Failed: ' . $e->getMessage(), [
                        'loan_id' => $loan->id,
                        'customer' => $customer->id
                    ]);
                }
            }
        }
    }
}
