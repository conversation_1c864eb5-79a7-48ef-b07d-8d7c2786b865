<?php


namespace App\Services;

use App\Actions\Loans\AffectAssetLoanDownPaymentAction;
use App\Actions\Loans\CreateApprovedLoanAction;
use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Actions\Loans\ProcessLoanRestructureAction;
use App\Actions\Savings\ProcessSavingsDepositAction;
use App\Actions\Savings\ProcessSavingsWithdrawalAction;
use Exception;
use App\Models\Transaction;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Services\Contracts\ProvidesTransactableAPIs;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\HttpException;

class AirtelOpenApiService implements ProvidesTransactableAPIs
{
    protected string $baseUrl = '';

    const STATUS_SUCCESS = "SUCCEEDED";
    const STATUS_PENDING = "PENDING";
    const STATUS_FAILED = "FAILED";


    public function __construct($environment, protected array $appConfiguration = [])
    {
        $env = strtolower($environment);

        $this->baseUrl = data_get(config('services.airtel'), "{$env}.url");
    }

    public function getStatusSuccessMessage(): string
    {
        return self::STATUS_SUCCESS; // Airtel specific success message
    }

    public function getServiceProviderName(): string
    {
        return 'AIRTEL';
    }
    /**
     * @throws ConnectionException
     */
    public function kyc($phoneNumber)
    {
        $response = $this->makeRequest()
            ->get(' /standard/v1/users/' . $phoneNumber);

        return $response->json();
    }

    /**
     * Make a request to the Airtel Open API for a collection.
     *
     * @param string $phone_number The phone number of the subscriber.
     * @param integer $amount The amount of money to be collected.
     * @param string $txn_reference The transaction reference from DAS.
     * @param string $reason The reason for the collection. Default is "Deposit".
     * @return array The response from the Airtel Open API.
     * @throws Exception If the request fails.
     */
    public function collect(
        $phone_number,
        $amount,
        $txn_reference,
        $reason = "Deposit"
    ): array {
        // Change the response appropriately once the airtel integration is complate.
        if (local() || testing() || staging()) {
            return [
                "status" => "TS",
                "status_code" => 1,
                "message" => $this->getStatusSuccessMessage(),
                "reference" => Str::random(),
            ];
        }
        $data = [
            "reference" => $reason,
            "subscriber" => [
                "country" => "UG",
                "currency" => "UGX",
                "msisdn" => str($phone_number)->after('256')->toString(),
            ],
            "transaction" => [
                "amount" => $amount,
                "country" => "UG",
                "currency" => "UGX",
                "id" => $txn_reference
            ]
        ];
        $response = $this->makeRequest()->post('/merchant/v2/payments/', $data);

        if ($response->failed()) {
            throw new Exception('Failed to request for collections: ' . $response->body()); // ?
        }

        $responseDetails = $response->json();

        return [
            'status' => data_get($responseDetails, 'data.transaction.status'),
            'message' => $this->getCollectionResponseMessage($responseDetails),
            'reference' => data_get($responseDetails, 'data.transaction.id'),
            'status_code' => data_get($responseDetails, 'status.response_code'),
        ];
    }

    /**
     * Get the status of a collection.
     *
     * @param string $txn_id The transaction id of the collection.
     * @return array The response from the Airtel Open API.
     * @throws Exception If the request fails.
     */
    public function collectionStatus($txn_id): array
    {
        // Change the response appropriately once the airtel integration is complete.
        if (local() || testing() || staging()) {
            return [
                "status" => "TS",
                "status_code" => 2,
                "message" => $this->getStatusSuccessMessage(),
                "reference" => $txn_id,
                "payment_reference" => mt_rand(100000000, 999999999),
                'payment_message' => 'Collection successful',
            ];
        }
        $response = $this->makeRequest()->get("/standard/v1/payments/$txn_id");

        if ($response->failed()) {
            throw new Exception('Failed to check collection status: ' . $response->body()); // ?
        }

        $responseDetails = $response->json();
        $transactionStatus = data_get($responseDetails, 'data.transaction.status');

        return [
            'status' => $transactionStatus,
            'message' => $this->getCollectionResponseMessage($responseDetails),
            'reference' => data_get($responseDetails, 'data.transaction.id'),
            'payment_reference' => data_get($responseDetails, 'data.transaction.airtel_money_id'),
            'payment_message' => data_get($responseDetails, 'data.transaction.message'),
            'status_code' => data_get($responseDetails, 'status.response_code'),
        ];
    }

    /**
     * Send money to a phone number.
     *
     * @param string $phone_number The phone number to send money to.
     * @param int $amount The amount of money to send.
     * @param string $txn_id The transaction id of the E-Wallet transaction.
     * @param string $reason The reason for the money transfer. Defaults to "Withdraw".
     * @return array The response from the Airtel Open API.
     * @throws Exception If the request fails.
     */
    public function disburse(
        string $phone_number,
        $amount,
        $txn_id,
        string $reason = "Withdraw"
    ): array {
        // Change the response appropriately once the airtel integration is complete.
        if (local() || testing() || staging()) {
            return [
                "status" => "TS",
                "status_code" => 0,
                "message" => $this->getStatusSuccessMessage(),
                "reference" => str()->random(10),
                'payment_reference' => mt_rand(100000000, 999999999),
                'payment_message' => 'Disbursement successful',
                "service_provider" => $this->getServiceProviderName(),
            ];
        }
        try {
            $data = [
                "payee" => [
                    "currency" => "UGX",
                    "msisdn" => str($phone_number)->after('256')->toString(),
                    "wallet_type" => 'NORMAL',
                ],
                "reference" => $reason,
                "pin" => $this->encrypt(Arr::get($this->appConfiguration, 'pin')),
                "transaction" => [
                    "amount" => (int) $amount,
                    "id" => $txn_id,
                    "type" => "B2C",
                ],
            ];

            $response = $this->makeRequest()->post('/standard/v2/disbursements/', $data);

            if ($response->failed()) {
                return [
                    'status' => 'FAILED',
                    'message' => 'FAILED',
                    'status_code' => $response->status(),
                    'payment_message' => Str::limit($response->body(), 255),
                    'reference' => null,
                    'payment_reference' => null,
                    'service_provider' => $this->getServiceProviderName(),
                ];
            }

            $responseDetails = $response->json();

            return [
                'status' => data_get($responseDetails, 'status.success'),
                'message' => $this->getDisbursementResponseMessage($responseDetails),
                'reference' => data_get($responseDetails, 'data.transaction.id'),
                'payment_reference' => data_get($responseDetails, 'data.transaction.reference_id'),
                'payment_message' => data_get($responseDetails, 'data.transaction.message'),
                'status_code' => data_get($responseDetails, 'status.code'),
                'data' => $responseDetails,
                'service_provider' => $this->getServiceProviderName(),
            ];
        } catch (Exception $e) {
            return $this->createErrorResponse($e->getMessage(), 500);
        }
    }

    protected function createErrorResponse(string $message, int $code): array
    {
        return [
            'status' => 'FAILED',
            'message' => 'FAILED',
            'status_code' => $code,
            'payment_message' => Str::limit($message, 255),
            'reference' => null,
            'payment_reference' => null,
            'service_provider' => $this->getServiceProviderName(),
        ];
    }

    /**
     * Retrieve the status of a disbursement initiated on the Airtel Open API.
     *
     * @param $txn_id
     * @return array The response from the Airtel Open API.
     * @throws ConnectionException
     */
    public function disbursementStatus($txn_id): array
    {
        if (local() || testing() || staging()) {
            return [
                "status" => "TS",
                "status_code" => 2,
                "message" => $this->getStatusSuccessMessage(),
                "reference" => $txn_id,
                'payment_reference' => mt_rand(100000000, 999999999),
                'payment_message' => 'Disbursement successful.',
            ];
        }

        $response = $this->makeRequest()->get("/standard/v2/disbursements/$txn_id", [
            'transactionType' => 'B2C',
        ]);

        if ($response->failed()) {
            [
                'status' => 'FAILED',
                'message' => 'FAILED',
                'status_code' => $response->status(),
                'payment_message' => Str::limit($response->body(), 255),
                'reference' => null,
                'payment_reference' => null,
                'service_provider' => $this->getServiceProviderName(),
            ];
            //throw new Exception('Failed to check disbursement status: ' . $response->body()); // ?
        }

        $responseDetails = $response->json();

        return [
            'status' => data_get($responseDetails, 'status.success'),
            'status_code' => data_get($responseDetails, 'status.code'),
            'message' => $this->getDisbursementResponseMessage($responseDetails),
            'reference' => data_get($responseDetails, 'data.transaction.id'),
            'payment_reference' => data_get($responseDetails, 'data.transaction.airtel_money_id'),
            'payment_message' => data_get($responseDetails, 'data.transaction.message'),
        ];
    }

    /**
     * Process a callback from the Airtel Open API.
     *
     * This function expects the transaction reference and status in the request body.
     * It will then attempt to find the transaction in the E-Wallet transaction requests table.
     * If the transaction is found, it will update the transaction's status and attempt to process the transaction.
     * If the transaction is not found, it will return a 400 response with a message indicating that the transaction was not found.
     * If the transaction has already been processed, it will return a 200 response with a message indicating that the transaction has already been processed.
     *
     * @param Request $request
     * @return bool
     * @throws Exception If the request fails.
     */
    public function processCallback(Transaction $transaction): bool
    {
        try {
            $txn_reference = $transaction->TXN_ID;

            if (!$txn_reference) {
                throw new Exception("Transaction with reference $txn_reference not found");
            }

            if (strtoupper($transaction->Status) == self::STATUS_SUCCESS) {
                throw new Exception("Transaction with reference $txn_reference already processed");
            }

            if ($transaction->Type == Transaction::DISBURSEMENT) {
                return $this->processLoanDisbursement($transaction);
            } else if ($transaction->Type == Transaction::DEPOSIT) {
                return $this->processSavingsDeposit($transaction);
            } else if ($transaction->Type == Transaction::WITHDRAW) {
                return $this->processSavingsWithdraw(transaction: $transaction);
            } else if ($transaction->Type == Transaction::REPAYMENT) {
                return $this->processLoanRepayment($transaction);
            } else if ($transaction->Type == Transaction::RESTRUCTURE) {
                return $this->processLoanRestructure($transaction);
            } else if ($transaction->Type == Transaction::DOWNPAYMENT) {
                return $this->processAssetLoanDownpayment($transaction);
            } else {
                throw new Exception("Invalid transaction type");
            }
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }

        return true;
    }

    /**
     * @throws Exception
     */
    private function processSavingsDeposit($transaction)
    {
        return app(ProcessSavingsDepositAction::class)->execute($transaction);
    }

    /**
     * @throws Exception
     */
    private function processSavingsWithdraw($transaction)
    {
        return app(ProcessSavingsWithdrawalAction::class)->execute($transaction);
    }

    /**
     * @throws Exception
     */
    private function processLoanRepayment($transaction): bool
    {
        return app(ProcessLoanRepaymentAction::class)->execute($transaction);
    }

    private function processLoanDisbursement(Transaction $transaction): true
    {
        return app(CreateApprovedLoanAction::class)->execute($transaction);
    }

    /**
     * Executed for
     * @param $transaction
     * @return true
     */
    public function processAssetLoanDownpayment($transaction): true
    {
        return app(AffectAssetLoanDownPaymentAction::class)->execute($transaction);
    }

    private function processLoanRestructure($transaction)
    {
        return app(ProcessLoanRestructureAction::class)->execute($transaction);
    }

    /**
     * @throws ConnectionException|Exception
     */
    public function getAccessToken()
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => '*/*',
            ])
                ->retry(3, 500)
                ->post($this->baseUrl . '/auth/oauth2/token', [
                    'client_id' => Arr::get($this->appConfiguration, 'client_key'),
                    'client_secret' => Arr::get($this->appConfiguration, 'client_secret'),
                    'grant_type' => 'client_credentials',
                ]);

            $token = Arr::get($response->json(), 'access_token');

            if ($response->failed() || empty($token)) {
                Log::error(json_encode($response->json()));
                throw new Exception('Failed to get access token: ' . $response->body());
            }

            return $token;
        } catch (ConnectionException $e) {
            Log::error($e->getMessage());
            throw new Exception('Failed to connect to Airtel: ' . $e->getMessage());
        } catch (Exception $e) {
            Log::error($e->getMessage());
            throw new Exception('Error: ' . $e->getMessage());
        }
    }

    protected function makeRequest(): PendingRequest
    {
        try {
            $token = $this->getAccessToken();

            if (empty($token)) {
                throw new \RuntimeException('Empty access token received');
            }

            return Http::withHeaders([
                "X-Country" => "UG",
                "X-Currency" => "UGX",
                "Content-Type" => "application/json",
                "Authorization" => "Bearer " . $token,
            ])->retry(3, 500)
                ->baseUrl($this->baseUrl);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    public function encrypt($data): string
    {
        $public_key = "-----BEGIN PUBLIC KEY-----\n" . Arr::get($this->appConfiguration, 'airtel_public_key') . "\n-----END PUBLIC KEY-----";

        $publicKey = openssl_pkey_get_public($public_key);

        if (! $publicKey) {
            throw new Exception('Public key malformed.');
        }

        if (! openssl_public_encrypt($data, $encrypted, $publicKey)) {
            throw new Exception('Error encrypting with public key');
        }

        return base64_encode($encrypted);
    }

    /**
     * @param mixed $responseDetails
     * @return string
     */
    public function getDisbursementResponseMessage(mixed $responseDetails): string
    {
        $transactionStatus = data_get($responseDetails, 'data.transaction.status');
        $transactionStatusCode = data_get($responseDetails, 'status.response_code');

        if ($transactionStatus === 'TS' || $transactionStatusCode === 'DP00900001001') {
            return self::STATUS_SUCCESS;
        }

        if (in_array($transactionStatusCode, ['DP00900001000', 'DP00900001006', 'DP00900001014'])) {
            return self::STATUS_PENDING;
        }

        return self::STATUS_FAILED;
    }

    /**
     * @param array $responseDetails
     * @return string
     */
    public function getCollectionResponseMessage(array $responseDetails): string
    {
        $transactionStatus = data_get($responseDetails, 'data.transaction.status');
        $transactionStatusCode = data_get($responseDetails, 'status.response_code');

        if ($transactionStatus === 'TS' || $transactionStatusCode === 'DP00800001001') {
            return self::STATUS_SUCCESS;
        }

        /**
         * The transaction is any of:
         * Ambiguous: The transaction is still processing and is in ambiguous state. Please do the transaction enquiry to fetch the transaction status.
         * In Process: Transaction in pending state. Please check after sometime.
         * Timed out: The transaction was timed out, can be failed or successful. Please check after sometime.
         */
        if (in_array($transactionStatusCode, ['DP00800001000', 'DP00800001006', 'DP00800001024'])) {
            return self::STATUS_PENDING;
        }

        return self::STATUS_FAILED;
    }
}
