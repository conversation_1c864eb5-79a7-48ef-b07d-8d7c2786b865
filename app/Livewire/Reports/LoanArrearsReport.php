<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetLoanArrearsReportDetailsAction;
use App\Actions\Reports\GetOutstandingLoanReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\LoanArrearsExport;

class LoanArrearsReport extends Component
{
    use WithPagination, ExportsData;

    public string $suspendedInterest;

    public function mount(): void
    {
        $this->endDate = now()->format('Y-m-d');
        $this->suspendedInterest = false;
    }

    public function render()
    {
        return view('livewire.reports.loan-arrears-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.loan-arrears', [
                'loans' => app(GetLoanArrearsReportDetailsAction::class)->filters($this->getFilters())->execute(),
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters()
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new LoanArrearsExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection
    {
        if (empty($this->endDate) && !$this->suspendedInterest) {
            return collect();
        }

        $filters = $this->getFilters();
        if ($this->suspendedInterest) {
            $filters['suspendedInterest'] = true;
        }

        return app(GetLoanArrearsReportDetailsAction::class)
            ->paginate()
            ->filters($filters)
            ->execute();
    }
}
