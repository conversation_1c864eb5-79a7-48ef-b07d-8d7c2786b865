<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetLoanLedgerReportDetailsAction;
use App\Exports\LoanLedgerExport;
use App\Models\Loan;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class LoanLedgerReport extends Component
{
    use ExportsData, WithPagination;

    public $loanId;
    public $loan;

    public function mount($loanId)
    {
        $this->loanId = $loanId;
        $this->loan = Loan::with(['customer', 'loan_product'])->find($loanId);

        // Set default date range to show all history
        $this->startDate = $this->loan->Credit_Account_Date->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.loan-ledger-report', [
            'records' => $this->getRecords(),
            'loan' => $this->loan
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.loan-ledger', [
                'records' => app(GetLoanLedgerReportDetailsAction::class)
                    ->forLoan($this->loanId)
                    ->filters($this->getFilters())
                    ->execute(),
                'loan' => $this->loan,
                'partnerName' => auth()->user()?->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters(),
            ])
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filters = $this->getFilters();
        $filters['loanId'] = $this->loanId;

        return Excel::download(new LoanLedgerExport($filters), $this->getExcelFilename());
    }

    private function getRecords()
    {
        return app(GetLoanLedgerReportDetailsAction::class)
            ->forLoan($this->loanId)
            ->filters($this->getFilters())
            ->paginate()
            ->execute();
    }

    /**
     * @return array
     */
    public function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'loanId' => $this->loanId,
        ];
    }

    public function clearDateFilters()
    {
        $this->startDate = '';
        $this->endDate = '';
        $this->resetPage();
    }

    public function applyDateFilters()
    {
        $this->resetPage();
    }

    /**
     * Get running balance for display
     */
    public function getRunningBalance($entries, $currentIndex): float
    {
        $balance = 0;

        for ($i = 0; $i <= $currentIndex; $i++) {
            $entry = $entries[$i];

            // For loan accounts, debits increase the balance (money owed)
            // Credits decrease the balance (payments received)
            if ($entry->accounting_type === 'debit') {
                $balance += $entry->debit_amount ?? $entry->amount;
            } else {
                $balance -= $entry->credit_amount ?? $entry->amount;
            }
        }

        return $balance;
    }
}
