<?php

namespace App\Livewire\OtherReports;

use App\Actions\OtherReports\GetDailyReconciliationReportDetailsAction;
use App\Actions\OtherReports\GetGeneralLedgerSummaryDetailsAction;
use App\Actions\Reports\GetPerformanceMetricsReportDetailsAction;
use App\Exports\PerformanceMetricsExport;
use App\Services\Account\AccountSeederService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class GeneralLedgerSummaryReport extends Component
{
    use WithPagination;

    public string $endDate = '';
    public string $startDate = '';

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.general-ledger-summary', $this->getViewData());
    }

    public function printReport()
    {
        // Format data for the report
        $filters = $this->getFilters();
        $filters['endDate'] = Carbon::parse($this->endDate)->format('d-m-Y');

        // Get data to pass to the report
        $viewData = $this->getViewData();
        $viewData['filters'] = $filters;
        $viewData['partner'] = auth()->user()->partner;

        $pdf = Pdf::loadView('pdf.general-ledger-summary', $viewData)
            ->setPaper('A4', 'landscape');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'general-ledger-summary-'.$this->endDate.'.pdf');
    }

    public function excelExport(): true
    {
        // todo: generate excel
        session()->flash('error', 'Exporting to excel is not yet supported for this report.');

        return true;
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetGeneralLedgerSummaryDetailsAction::class)
            ->filters($this->getFilters())
            ->execute();
    }

    protected function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    protected function getViewData(): array
    {
        $records = $this->getReportData();

        return [
            'records' => $records
        ];
    }
}
