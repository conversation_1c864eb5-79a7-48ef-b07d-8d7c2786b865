<?php

namespace App\Livewire\Loans;

use Livewire\Component;
use App\Models\CustomerAsset;
use App\Actions\Loans\ReposessAssetAction;
use App\Actions\Loans\UpdateAssetLocationAction;

class LoanAssetsTable extends Component
{
    public string $search = '';

    public string $assetStatus = '';

    public function render()
    {
        return view('livewire.loans.loan-assets-table', [
            'records' => $this->getRecords()
        ]);
    }

    public function getRecords()
    {
        $query = CustomerAsset::query()
            ->has('loan')
            ->with(['loan', 'customer']);

        if ($this->assetStatus) {
            $query->where('Status', $this->assetStatus);
        }

        if ($this->search) {
            $query->where(function ($query) {
                $query->where('identification', 'like', '%' . $this->search . '%')
                    ->orWhereRelation('customer', 'first_name', 'like', '%' . $this->search . '%')
                    ->orWhereRelation('customer', 'last_name', 'like', '%' . $this->search . '%')
                    ->orWhereRelation('customer', 'telephone_number', 'like', '%' . $this->search . '%')
                    ->orWhereRelation('loan', 'id', 'like', '%' . $this->search . '%');
            });
        }

        return $query->get();
    }

    public function reposess($identification, $loanId): void
    {
        $action = app(ReposessAssetAction::class);
        $action->execute($identification, $loanId);
    }

    public function refreshLocation($identification, $loanId): void
    {
        $action = app(UpdateAssetLocationAction::class);

        $action->execute($identification, $loanId);
    }
}
