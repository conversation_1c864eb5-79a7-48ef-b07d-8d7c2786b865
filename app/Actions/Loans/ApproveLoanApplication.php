<?php

namespace App\Actions\Loans;

use Illuminate\Support\Str;
use App\Models\LoanApplication;
use App\Events\LoanApplicationApproved;
use Illuminate\Http\Request;

class ApproveLoanApplication
{
    public function execute(LoanApplication $loanApplication, Request $request)
    {
        /**
         * Updating both the loan application and the down payment transaction
         * We shall be switching to storing approvals on the loan application and eventually deprecate the down payment transaction approval.
         *
         * @todo: Refactor to store approvals on the loan application only
         */

        $loanApplication->update([
            'Credit_Application_Status' => 'Approved',
            'Approved_By' => auth()->id(),
            'Approval_Date' => now()->toDateTimeString(),
            'Approval_Reference' => $request->Approval_Reference,
            'Approval_Narration' => $request->Approval_Narration,
        ]);

        if ($loanApplication->downPayment) {
            $loanApplication->downPayment->update([
                'Asset_Disbursement_Status' => 'Approved',
                'Approval_Date' => now()->toDateTimeString(),
                'Approval_Reference' => $request->Approval_Reference,
            ]);
        }

        event(new LoanApplicationApproved($loanApplication));
    }
}
