<?php

namespace App\Actions;

use App\Models\CustomerAsset;
use App\Services\SpiroApiService;
use Illuminate\Support\Facades\Log;

class ImmobilizeBikeAction
{
    public function execute($bikeNumber): void
    {
        try {
            $asset = CustomerAsset::query()->firstWhere('Identification', $bikeNumber);
            if (empty($asset)) {
                return;
            }
            $bikeProvider = new SpiroApiService();
            $bikeProvider->immobilizeBike($bikeNumber);
            $asset->update([
                'Disabled_At' => now(),
                'Last_Disabled_At' => now(),
            ]);
        } catch (\Throwable $th) {
            logger()->error($th->getMessage());
            return;
        }
    }
}
