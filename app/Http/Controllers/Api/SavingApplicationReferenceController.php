<?php

namespace App\Http\Controllers\Api;

use App\Jobs\ChatBot\DownloadFile;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\SavingsApplication;
use Illuminate\Support\Facades\Validator;

class SavingApplicationReferenceController extends Controller
{
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reference_code' => ['required', 'exists:savings_applications,Reference_Code'],
            'nin_back' => ['nullable', 'string', 'required_with:nin_front'],
            'nin_front' => ['nullable', 'string', 'required_with:nin_back'],
            'residence_proof' => ['nullable', 'string', 'required_without:nin_front,nin_back'],
            'phone' => ['required', 'string', 'max:13'],
            'message_id' => ['required', 'string', 'max:255'],
            'document_type' => ['required', 'string', 'max:25'],
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()->all()], 422);
        }

        dispatch(new DownloadFile($validator->validated()));

        return response()->json([
            'status' => 'success',
        ]);
    }

    public function show(Request $request, $referenceCode): \Illuminate\Http\JsonResponse
    {
        $savingApplication = SavingsApplication::query()->firstWhere('Reference_Code', $referenceCode);

        if (empty($savingApplication)) {
            return response()->json([
                'status' => 'failed',
                'message' => 'Savings application not found',
            ], 404);
        }

        // todo: Use a Api Resource for this
        $details = $savingApplication->only(['Reference_Code', 'Document_Status', 'Amount']);
        $details['created_at'] = $savingApplication->created_at->toDayDateTimeString();
        $details['application_amount'] = $savingApplication->amount;
        $details['payment_type'] = $savingApplication->customer?->savingPreference?->Payment_Type;

        $customerDetails = [
            'name' => $savingApplication->customer->name,
            'telephone_number' => $savingApplication->customer->Telephone_Number,
        ];

        $data = collect($details)->merge($customerDetails)->mapWithKeys(function ($item, $key) {
            return [strtolower($key) => $item];
        })->toArray();

        return response()->json([
            'status' => 'success',
            'data' => $data,
        ]);
    }
}
