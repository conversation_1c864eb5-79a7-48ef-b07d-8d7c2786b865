<?php

namespace App\Models;

use App\Mail\LoanApplicationApproved;
use App\Models\Partner;
use App\Models\Scopes\PartnerScope;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{
    use HasFactory;

    const WITHDRAW = 'Withdraw';
    const DEPOSIT = 'Deposit';
    const DISBURSEMENT = 'Disbursement';
    const REPAYMENT = 'Repayment';
    const DOWNPAYMENT = 'Downpayment';
    const RESTRUCTURE = 'Restructure';

    protected $fillable = [
        'Partner_ID',
        'Type',
        'Status',
        'Telephone_Number',
        'Amount',
        'TXN_ID',
        'Asset_Provider_ID', // Could change to asset partner id
        'Provider_TXN_ID',
        'Payment_Reference',
        'Narration',
        'Loan_ID',
        'Loan_Application_ID',
        'Asset_Provider',
        'Asset_Disbursement_Status',
        'Retry_Count',
        'Approval_Reference',
        'Approval_Date',
        'Rejection_Reference',
        'Rejection_Date',
        'Restructure_Days',
        'Savings_Product_Fee_ID',
        'Payment_Service_Provider',
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);
    }


    public static function generateID()
    {
        return Str::uuid();
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class, 'Partner_ID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'Telephone_Number', 'Telephone_Number');
    }

    public function fee(): BelongsTo
    {
        return $this->belongsTo(SavingsProductFee::class, 'Savings_Product_Fee_ID');
    }


    public function loan()
    {
        return $this->belongsTo(Loan::class, 'Loan_ID');
    }


    public function loanApplication()
    {
        return $this->belongsTo(LoanApplication::class, 'Loan_Application_ID');
    }

    public function duration(): Attribute
    {
        return Attribute::make(fn() => round(now()->diffInDays($this->created_at, true)));
    }

    public function canBeSubmitted(): bool
    {
        return strtolower($this->Asset_Disbursement_Status) === 'pending' && $this->duration <= 100;
    }

    public function canBeDisbursed(): bool
    {
        return is_null($this->Loan_ID) && ! is_null($this->Approval_Date) && ! is_null($this->Approval_Reference);
    }

    public function canBeRefunded(): bool
    {
        return $this->duration > 14 && strtolower($this->Asset_Disbursement_Status) !== 'refunded';
    }
}
