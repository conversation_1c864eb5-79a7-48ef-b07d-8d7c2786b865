<?php

namespace App\Models;

use App\Enums\AccountingType;
use Carbon\Carbon;
use App\Models\Loan;
use App\Models\Customer;
use App\Models\Accounts\Account;
use App\Models\Scopes\PartnerScope;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Services\Account\AccountSeederService;
use App\Models\Transactables\Contracts\Transactable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;

class LoanDisbursement extends Model implements Transactable
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        "customer_id",
        "partner_id",
        "loan_id",
        "disbursement_date",
        "amount",
        "partner_notified",
        "partner_notified_date"
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);
    }

    public static function createDisbursement(Loan $loan): LoanDisbursement
    {
        return LoanDisbursement::create([
            "loan_id" => $loan->id,
            "disbursement_date" => Carbon::now(),
            "amount" => $loan->Credit_Amount,
            "partner_id" => $loan->Partner_ID,
            "customer_id" => $loan->Customer_ID,
        ]);
    }

    public function journalEntries(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(JournalEntry::class, 'journable', 'transactable', 'transactable_id')->chaperone();
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }


    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function amount()
    {
        return $this->amount;
    }

    public function affectAccounts(): void
    {
        $disbursement_account = Account::where('partner_id', $this->partner_id)
            ->where('slug', AccountSeederService::DISBURSEMENT_OVA_SLUG)
            ->first();
        if ($disbursement_account->balance < $this->amount()) {
            Log::error("Insufficient balance in the loan escrow account" . $disbursement_account->id);
            throw new Exception("Insufficient balance in the loan escrow account -" . $disbursement_account->id);
        }

        $loan_product = $this->loan->loan_product;

        $loan_product_account = $loan_product->general_ledger_account;

        $txn = [];
        $txn_id = rand(11111, 99999) . "-" . now()->unix();
        $total_ledger_fees_amount = 0;
        // Arrangement Fee - Goes to Other Income Account for now
        $disbursementFees = $this->loan->fees->where('Charge_At', 'Disbursement')->where('Status', 'Fully Paid');
        foreach ($disbursementFees as $disbursementFee) {
            $fees_account = Account::find($disbursementFee->Payable_Account_ID);

            if (!$fees_account) {
                $fees_account = Account::where("partner_id", $loan_product->Partner_ID)
                    ->where("slug", AccountSeederService::INCOME_FROM_FINES_SLUG)
                    ->first();
            }

            $cashType = 'Cash In';
            $total_fees_amount = $disbursementFee->Amount;

            $fees_account_previous_balance = $fees_account->balance;
            $fees_account_balance = $fees_account_previous_balance + $total_fees_amount;
            $fees_account->balance = $fees_account_balance;
            $fees_account->save();
            $txn[] = [
                'customer_id' => $this->loan->Customer_ID,
                'account_id' => $fees_account->id,
                'amount' => $total_fees_amount,
                'transactable_id' => $this->id,
                'transactable' => LoanDisbursement::class,
                'partner_id' => $this->partner_id,
                'txn_id' => $txn_id,
                'account_name' => $fees_account->name,
                'cash_type' => $cashType,
                'previous_balance' => $fees_account_previous_balance,
                'current_balance' => $fees_account->balance,
                'accounting_type' => 'Credit',
                'debit_amount' => 0,
                'credit_amount' => $total_fees_amount,
                'created_at' => now(),
                'updated_at' => now(),
            ];
            $total_ledger_fees_amount += $total_fees_amount;
        }
        // Debit Record
        $loan_product_account_previous_balance = $loan_product_account->balance;
        $loan_product_account_balance = $loan_product_account_previous_balance + $this->amount();
        $loan_product_account->balance = $loan_product_account_balance;
        $loan_product_account->save();
        $txn[] = [
            'customer_id' => $this->loan->Customer_ID,
            'account_id' => $loan_product_account->id,
            'amount' => $this->amount(),
            'transactable_id' => $this->id,
            'transactable' => LoanDisbursement::class,
            'partner_id' => $this->partner_id,
            'txn_id' => $txn_id,
            'account_name' => $loan_product_account->name,
            'cash_type' => 'Cash In',
            'previous_balance' => $loan_product_account_previous_balance,
            'current_balance' => $loan_product_account->balance,
            'accounting_type' => 'Debit',
            'credit_amount' => 0,
            'debit_amount' => $this->amount(),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Credit Record
        $entryAmount = $this->amount() - $total_ledger_fees_amount;
        $disbursement_account_previous_balance = $disbursement_account->balance;
        $disbursement_account->decrement('balance', $entryAmount);

        $txn[] = [
            'customer_id' => $this->loan->Customer_ID,
            'account_id' => $disbursement_account->id,
            'amount' => $entryAmount,
            'transactable_id' => $this->id,
            'transactable' => LoanDisbursement::class,
            'partner_id' => $this->partner_id,
            'txn_id' => $txn_id,
            'account_name' => $disbursement_account->name,
            'cash_type' => 'Cash In',
            'previous_balance' => $disbursement_account_previous_balance,
            'current_balance' => $disbursement_account->balance,
            'accounting_type' => 'Credit',
            'credit_amount' => $entryAmount,
            'debit_amount' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];


        if ($loan_product->isAssetLoan()) {
            $lms_ussd_session = LmsUssdSessionTracking::where('Loan_Application_ID', $this->loan->loan_application->id)
                ->first();
            if (!$lms_ussd_session) {
                Log::error("Asset loans requires an LMS USSD session to have been created. This is an irreconcilable asset loan.");
                throw new Exception("Asset loans requires an LMS USSD session to have been created. This is an irreconcilable asset loan.");
            }
            if ($lms_ussd_session->Insurance_Amount) {
                // Insurance
                $insurance_payable_account = Account::where('partner_id', $this->partner_id)
                    ->where("slug", AccountSeederService::INSURANCE_RECEIVABLES_SLUG)
                    ->first();
                if (!$insurance_payable_account) {
                    throw new Exception("Asset loans requires an insurance account to be created. Please create one.");
                }
                $total_insurance_amount = $lms_ussd_session->Insurance_Amount;
                $insurance_payable_account_previous_balance = $insurance_payable_account->balance;
                $insurance_payable_account_balance = $insurance_payable_account_previous_balance + $total_insurance_amount;
                $insurance_payable_account->balance = $insurance_payable_account_balance;
                $insurance_payable_account->save();
                $txn[] = [
                    'customer_id' => $this->loan->Customer_ID,
                    'account_id' => $insurance_payable_account->id,
                    'amount' => $total_insurance_amount,
                    'transactable_id' => $this->id,
                    'transactable' => LoanDisbursement::class,
                    'partner_id' => $this->partner_id,
                    'txn_id' => $txn_id,
                    'account_name' => $insurance_payable_account->name,
                    'cash_type' => 'Cash In',
                    'previous_balance' => $insurance_payable_account_previous_balance,
                    'current_balance' => $insurance_payable_account->balance,
                    'accounting_type' => 'Debit',
                    'credit_amount' => 0,
                    'debit_amount' => $total_insurance_amount,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            if (!$lms_ussd_session->hasPermit && $lms_ussd_session->Permit_Amount) {
                // Permit
                $permit_payable_account = Account::where('partner_id', $this->partner_id)
                    ->where("slug", AccountSeederService::PERMIT_RECEIVABLES_SLUG)
                    ->first();
                if (!$permit_payable_account) {
                    throw new Exception("Asset loans requires an insurance account to be created. Please create one.");
                }
                // Permint if any
                $total_permit_amount = $lms_ussd_session->Permit_Amount;
                $permit_payable_account_previous_balance = $permit_payable_account->balance;
                $permit_payable_account_balance = $permit_payable_account_previous_balance + $total_permit_amount;
                $permit_payable_account->balance = $permit_payable_account_balance;
                $permit_payable_account->save();
                $txn[] = [
                    'customer_id' => $this->loan->Customer_ID,
                    'account_id' => $permit_payable_account->id,
                    'amount' => $total_permit_amount,
                    'transactable_id' => $this->id,
                    'transactable' => LoanDisbursement::class,
                    'partner_id' => $this->partner_id,
                    'txn_id' => $txn_id,
                    'account_name' => $permit_payable_account->name,
                    'cash_type' => 'Cash In',
                    'previous_balance' => $permit_payable_account_previous_balance,
                    'current_balance' => $permit_payable_account->balance,
                    'accounting_type' => 'Debit',
                    'credit_amount' => 0,
                    'debit_amount' => $total_permit_amount,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Asset payable account
            // Affect the Asset Payable account with principal down payment
            $asset_payable_account = $loan_product->payable_account;
            if (!$asset_payable_account) {
                Log::error("Asset loans require payable accounts to be created. Please create one.");
                throw new Exception("Asset loans require payable accounts to be created. Please create one.");
            }
            $asset_payable_account_previous_balance = $asset_payable_account->balance;
            $asset_payable_account_balance = $asset_payable_account_previous_balance + $this->amount();
            $asset_payable_account->balance = $asset_payable_account_balance;
            $asset_payable_account->save();
            $txn[] = [
                'customer_id' => $this->loan->Customer_ID,
                'account_id' => $asset_payable_account->id,
                'amount' => $this->amount(),
                'transactable_id' => $this->id,
                'transactable' => LoanDisbursement::class,
                'partner_id' => $this->partner_id,
                'txn_id' => $txn_id,
                'account_name' => $asset_payable_account->name,
                'cash_type' => 'Cash Out',
                'previous_balance' => $asset_payable_account_previous_balance,
                'current_balance' => $asset_payable_account->balance,
                'accounting_type' => 'Credit',
                'credit_amount' => $this->amount(),
                'debit_amount' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // If accounting type is accruals
        if ($this->partner->Accounting_Type == AccountingType::Accrual->value) {

            // todo: Check if the payment period. For daily repayment the posting should be done at midnight to recognize the accrued interest.
            $interestAmount = $this->loan->totalInterest() / $this->loan->Term;
            $interestReceivablesAccount = Account::where("partner_id", $this->loan->Partner_ID)
                ->where("slug", AccountSeederService::INTEREST_RECEIVABLES_SLUG)
                ->first();
            $interestReceivablesAccount_previous_balance = $interestReceivablesAccount->balance;
            $interestReceivablesAccount_balance = $interestReceivablesAccount_previous_balance + $interestAmount;
            $interestReceivablesAccount->balance = $interestReceivablesAccount_balance;
            $interestReceivablesAccount->save();
            $txn[] = [
                'customer_id' => $this->loan->Customer_ID,
                'account_id' => $interestReceivablesAccount->id,
                'amount' => $interestAmount,
                'transactable_id' => $this->id,
                'transactable' => LoanDisbursement::class,
                'partner_id' => $this->partner_id,
                'txn_id' => $txn_id,
                'account_name' => $interestReceivablesAccount->name,
                'cash_type' => 'Cash In',
                'previous_balance' => $interestReceivablesAccount_previous_balance,
                'current_balance' => $interestReceivablesAccount->balance,
                'accounting_type' => 'Debit',
                'credit_amount' => 0,
                'debit_amount' => $interestAmount,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $incomeFromInterestAccount = Account::where("partner_id", $this->loan->Partner_ID)
                ->where("slug", AccountSeederService::INTEREST_INCOME_FROM_LOANS_SLUG)
                ->first();

            $incomeFromInterestAccountBalance = $incomeFromInterestAccount->balance;
            $incomeFromInterestAccount->increment('balance', $interestAmount);

            $txn[] = [
                'customer_id' => $this->customer_id,
                'account_id' => $incomeFromInterestAccount->id,
                'amount' => $interestAmount,
                'transactable_id' => $this->id,
                'transactable' => LoanDisbursement::class,
                'partner_id' => $this->partner_id,
                'txn_id' => $txn_id,
                'account_name' => $incomeFromInterestAccount->name,
                'cash_type' => 'Cash In',
                'previous_balance' => $incomeFromInterestAccountBalance,
                'current_balance' => $incomeFromInterestAccount->balance,
                'accounting_type' => 'Credit',
                'credit_amount' => $interestAmount,
                'debit_amount' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        JournalEntry::insert($txn);
    }
}
