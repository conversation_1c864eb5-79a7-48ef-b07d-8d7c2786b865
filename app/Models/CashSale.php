<?php

namespace App\Models;

use App\Models\Scopes\PartnerScope;
use Illuminate\Database\Eloquent\Model;

class CashSale extends Model
{
    protected $fillable = [
        'receipt_number',
        'customer_name',
        'customer_phone',
        'customer_location',
        'amount',
        'receipt_path',
        'partner_id',
        'vin_no',
        'reg_no',
        'tin',
        'usage',
        'sales_executive',
        'lead_source',
        'financier',
    ];

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);
    }
}
