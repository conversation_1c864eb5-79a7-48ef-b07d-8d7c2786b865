<?php

namespace App\Listeners;

use App\Events\DisbursementDocumentsUploaded;
use App\Models\Partner;
use App\Models\Transaction;
use App\Mail\AssetDisbursed;
use App\Events\LoanDisbursed;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendDisbursementDocumentsUploadedMail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DisbursementDocumentsUploaded $event): void
    {
        $partner = $event->loanApplication->partner;
        $transaction = Transaction::query()->firstWhere('Loan_ID', $event->loanApplication->id);

        $assetPartner = Partner::query()->firstWhere('Institution_Name', 'like', '%'.$transaction?->Asset_Provider.'%');

        if ($partner?->Email_Notification_Recipients) {
            $recipients = array_map('trim', explode(',', $partner->Email_Notification_Recipients));
            $mail = Mail::to($recipients);

            if (! empty($assetPartner->Email_Notification_Recipients)) {
                $recipients = array_map('trim', explode(',', $assetPartner->Email_Notification_Recipients));
                $mail->cc($recipients);
            }

            $mail->send(new \App\Mail\DisbursementDocumentsUploaded($event->loanApplication));
        }
    }
}
