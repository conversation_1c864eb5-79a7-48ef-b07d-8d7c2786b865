<?php

namespace App\Console\Commands\DataImport;

use App\Models\Customer;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\LoanProduct;
use App\Models\LoanSchedule;
use App\Models\Scopes\PartnerScope;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use League\Csv\Exception;
use League\Csv\Reader;
use League\Csv\Statement;
use League\Csv\UnavailableStream;

class ImportSafeBodaData extends Command
{
    protected ?int $loanProductTermId = null;

    protected ?string $loanProductCode = null;

    protected int $interestRate = 10;

    protected array $failedImports = [];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:import-safeboda-data {--file=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Safe Boda Data. Use --file to specify the csv file (no spaces in filename).';

    protected $attributes = [
        'loan_manager_user_id',
        'driver_safeboda_number',
        'vehicle_type',
        'driver_type',
        'last_service_transaction_date',
        'current_status',
        'loan',
        'loan_description',
        'loan_creation_date',
        'last_loan_update_date',
        'loan_duration_in_days',
        'loan_principal_amount',
        'loan_balance',
        'driver_name',
        'driver_phone_number',
        'id_type',
        'driver_id_number',
        'loan_product',
        'loan_tenure_months',
        'interest_rate',
        'penalties',
    ];

  /**
   * Execute the console command.
   * @throws UnavailableStream
   * @throws Exception
   */
    public function handle(): int
    {
      $csv = Reader::createFromStream(fopen(Storage::path('partners/safeboda/'.$this->option('file')), 'r'));
      $csv->setHeaderOffset(0);

      $records = $csv->getRecords($this->attributes);
      $totalRecords = $csv->count();

      $this->info('Importing ('.$totalRecords.' raw records).');

      $bar = $this->output->createProgressBar($csv->count());

      $rawRecords = [];
      $lap = 0;

      foreach ($records as $key => $record) {
        $record['submission_date'] = now()->startOfMonth()->toDateString();

        try {
          DB::table('safeboda_imports')->upsert($record, [
            'loan_manager_user_id' => $record['loan_manager_user_id'],
            'submission_date' => $record['submission_date'],
          ]);
        } catch (\Throwable $th) {
          $this->failedImports[] = $record['loan_manager_user_id'];
          $this->error('Import failed: '.$record['driver_safeboda_number']);
        }

        $bar->advance();
      }

      $bar->finish();

      if (! empty($this->failedImports)) {
        $this->alert('Failed importing: '.implode(', ', $this->failedImports));
      }

      $this->line('');
      $this->info('Import completed: - '.($totalRecords - count($this->failedImports)).' passed out of '.$totalRecords.' records.');

      return 0;
    }
}
