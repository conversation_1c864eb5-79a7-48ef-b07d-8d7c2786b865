<?php

namespace App\Console\Commands;

use App\Models\Partner;
use App\Models\Transaction;
use App\Models\UssdHit;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetUssdHits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:get-ussd-hits {--partner=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $appUrl = env('APP_URL');
            if ($appUrl == 'https://lms-test.gnugridcrb.com') {
                $this->error('We should not run this on the test server');
                return 0;
            }
            $partner = $this->option('partner');
            if (!$partner) {
                $this->error('No partner provided');
                return 0;
            }

            $partner = Partner::where('Identification_Code', $partner)->first();
            if (!$partner) {
                $this->error('Partner ID not found');
                return 0;
            }
            $lastUssdHit = UssdHit::where('partner_id', $partner->id)->latest()->first();
            $accessToken = $this->getAccessToken();
            // Step 2: Use the access token to call the Loan Market API
            $apiResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Bearer ' . $accessToken,
            ])->post(env('CRB_URL') . '/v1/loan-market/partner-hits', [
                'channel' => 'USSD',
                'partnerCode' => $partner->Identification_Code,
                'lastSessionId' => $lastUssdHit ? $lastUssdHit->id : null,
                'numberOfRecords' => 500
            ]);



            if ($apiResponse->successful()) {
                $this->info('API call successful: ' . $apiResponse->body());
                // Step 3: Store the ussd_hits data in the database
                $responseData = $apiResponse->json(); // Assuming the response is an array of ussd_hits

                if (is_array($responseData)) {
                    $batchData = [];
                    foreach ($responseData as $item) {
                        // Your UTC timestamp
                        $utcTimestamp = $item['created_at'];
                        $date = Carbon::parse($utcTimestamp);
                        $date->setTimezone('Africa/Nairobi'); // Nairobi is in the EAT timezone
                        $createdAt = $date->format('Y-m-d H:i:s');

                        $batchData[] = [
                            'id' => $item['id'],
                            'name' => $item['name'],
                            'phone' => $item['phone'],
                            'created_at' => $createdAt, // Use the formatted datetime
                            'score' => $item['score'],
                            'score_band' => $item['score_band'],
                            'monthly_turnover' => $item['monthly_turnover'],
                            'partner_id' => $partner->id,
                            'phone_count' => $item['phone_count']
                        ];
                    }

                    // Insert the data in batches
                    $batchSize = 500; // Number of records per batch
                    foreach (array_chunk($batchData, $batchSize) as $batch) {
                        UssdHit::insertOrIgnore($batch);
                    }
                    $this->info('Data stored successfully in the ussd_hits table.');
                } else {
                    $this->error('Invalid response format. Expected an array.');
                }
            } else {
                $this->error('API call failed: ' . $apiResponse->body());
            }


            return 0;
        } catch (Exception $e) {
            $this->error('Error caught: ' . $e->getMessage());
        }
    }

    protected function getAccessToken(): string
    {
        $accessToken = Cache::get('api_access_token');
        if ($accessToken) {
            return $accessToken;
        } else {
            // Step 1: Get the access token
            $tokenResponse = Http::asForm()->post(env('CRB_URL') . '/v1/oauth/token', [
                'grant_type' => 'client_credentials',
                'client_id' => env('CRB_CLIENT_ID'),
                'client_secret' => env('CRB_CLIENT_SECRET'),
            ]);
            if ($tokenResponse->successful()) {
                $accessToken = $tokenResponse->json()['access_token'];
                $tokenLifeTime = $tokenResponse->json()['expires_in']; // seconds
                Cache::put('api_access_token', $accessToken, $tokenLifeTime);
                return $accessToken;
            } else {
                $this->error('Failed to retrieve access token: ' . $tokenResponse->body());
                throw new Exception('Failed to get access token');
            }
        }
    }

    protected function getTransactionSummary()
    {
        $data = Transaction::select([DB::raw('DATE(created_at) as date'), DB::raw('COUNT(DISTINCT Telephone_Number) as customer_count'), DB::raw('COUNT(*) as total_transactions'), DB::raw('COUNT(CASE WHEN Status = "Completed" THEN 1 END) as successful_transactions'), DB::raw('SUM(CASE WHEN Status = "Completed" THEN Amount ELSE 0 END) as successful_payment')])->where('Partner_ID', 2)->whereBetween('created_at', ['2025-03-06', '2025-04-31'])->groupBy('date')->get();
        return $data;
    }
}
