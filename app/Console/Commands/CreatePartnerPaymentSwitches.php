<?php

namespace App\Console\Commands;

use App\Actions\CreatePartnerPaymentSwitchAction;
use App\Models\Partner;
use App\Services\LoanDefaulterManager;
use App\Services\SpiroApiService;
use Illuminate\Console\Command;

class CreatePartnerPaymentSwitches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:partner-switches';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update payment switches';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $partners = Partner::all();

        foreach ($partners as $partner) {
            app(CreatePartnerPaymentSwitchAction::class)->execute($partner);
        }

        return 0;
    }
}
