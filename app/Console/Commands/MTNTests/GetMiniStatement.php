<?php

namespace App\Console\Commands\MTNTests;

use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use App\Exceptions\MtnApiException;
use Illuminate\Console\Command;

class GetMiniStatement extends Command
{
    protected $signature = 'mtn:statement
                          {msisdn : Customer phone number (format: 256XXXXXXXXX)}';

    protected $description = 'Test MTN get mini statement';

    public function handle(): int
    {
        try {
            $msisdn = $this->argument('msisdn');

            $this->info("Fetching mini statement - last 5 transactions...");
            $this->line("MSISDN: $msisdn");

            $mtnService = new MockMtnApiService();
            $response = $mtnService->getMiniStatement($msisdn);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }
}
