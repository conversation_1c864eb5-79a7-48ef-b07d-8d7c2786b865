<?php

namespace App\Console\Commands\MTNTests;

use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class AutoSweep extends Command
{
    protected $signature = 'mtn:sweep {loanId}';
    protected $description = 'Test MTN auto sweep';

    public function handle(): int
    {
        try {
            $loan = Loan::query()->findOrFail($this->argument('loanId'));

            if ($loan->Maturity_Date->isFuture()) {
                $this->error('Loan is not in arrears.');

                return 1;
            }

            $transactionRecord = [
                'Partner_ID' => $loan->Partner_ID,
                'Type' => Transaction::REPAYMENT,
                'Amount' => $loan->totalOutstandingBalance(),
                'Status' => 'Pending',
                'Telephone_Number' => $loan->customer->Telephone_Number,
                'TXN_ID' => Transaction::generateID(),
                'Loan_ID' => $loan->id,
                'Loan_Application_ID' => $loan->Loan_Application_ID,
                'Provider_TXN_ID' => null,
                'Payment_Reference' => null,
            ];

            $transaction = new Transaction($transactionRecord);

            $transaction->save();

            $api = (new PaymentServiceManager($transaction))->paymentService;
            $response = $api->autoCollect($transaction);

            $this->info(json_encode($response));
            return 0;
        } catch (\Exception $e) {
            $this->error($e->getMessage());

            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
