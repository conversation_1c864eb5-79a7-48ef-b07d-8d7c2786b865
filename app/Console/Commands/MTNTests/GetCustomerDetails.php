<?php

namespace App\Console\Commands\MTNTests;

use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use App\Exceptions\MtnApiException;
use Illuminate\Console\Command;

class GetCustomerDetails extends Command
{
    protected $signature = 'mtn:customer
                          {msisdn : Customer phone number (format: 256XXXXXXXXX)}';

    protected $description = 'Test MTN get customer details API';

    public function handle(): int
    {
        try {
            $msisdn = $this->argument('msisdn');

            $this->info("Fetching customer details...");
            $this->line("MSISDN: $msisdn");

            $mtnService = new MockMtnApiService();
            $response = $mtnService->getCustomerDetails($msisdn);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }
}
