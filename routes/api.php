<?php

use App\Http\Controllers\Api\MtnAgentController;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\EnsureApiKeyIsValid;
use App\Http\Controllers\Api\LoansApiController;
use App\Http\Controllers\Api\SavingsApiController;
use App\Http\Controllers\UssdChannelApiController;
use App\Http\Controllers\Api\CustomerApiController;
use App\Http\Controllers\LoanProductController;
use App\Http\Middleware\EnsurePartnerCodeIsProvided;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::prefix('v1')
    ->group(function () {
   Route::post('customerdetails', [MtnAgentController::class, 'customerDetails']);
   Route::post('customerregistration', [MtnAgentController::class, 'registerCustomer']);
   Route::post('customerregistrationcompleted', [MtnAgentController::class, 'completeCustomerRegistration']);
   //Route::post('loanapplicationcompleted', [MtnAgentController::class, 'completeLoanApplication']);
   Route::post('bankdebitcompleted', [MtnAgentController::class, 'bankDebitCompleted']);
});

// CUSTOMERS. Not authenticated because customers are not scoped to the partners
Route::put('/customers/update', [CustomerApiController::class, 'update'])->name('customers.update');
Route::post('/customers/create', [CustomerApiController::class, 'store'])->name('customers.store');
Route::put('/customers/barn', [CustomerApiController::class, 'barn'])->name('customers.barn');
Route::put('/customers/unbarn', [CustomerApiController::class, 'unbarn'])->name('customers.unbarn');
Route::delete('/customers/delete', [CustomerApiController::class, 'destroy'])->name('customers.destroy');
Route::put('/customers/restore', [CustomerApiController::class, 'restore'])->name('customers.restore');
Route::get('/partners/{id}', function ($id) {
    return Partner::where('id', $id)->first();
});

// LOAN APPLICATION
Route::post('/partners/{partner}/transactions/approve', [\App\Http\Controllers\Api\TransactionApiController::class, 'approve'])
    ->name('partners.transactions.approve');
Route::get('/loan-products/group-products', [LoanProductController::class, 'groupProducts']);
Route::post('/savings-applications/update-status', [SavingsApiController::class, 'updateStatus']);

// Webhooks
Route::post('/airtel/transaction-callback/{PartnerID}', [LoansApiController::class, 'airtelTransactionCallback'])
    ->name('airtel.callback');
Route::post('/mtn/transaction-callback/{PartnerID}', [LoansApiController::class, 'mtnTransactionCallback'])
    ->name('mtn.callback');
Route::post('/webhooks/auto-sweep', [\App\Http\Controllers\Api\AutoSweepWebhookController::class, 'store']);

Route::middleware([EnsureApiKeyIsValid::class])->group(function () {
    Route::post('/leads/materialized', \App\Http\Controllers\Api\RecordMaterializedLead::class);
});

// Protected routes
Route::middleware([EnsurePartnerCodeIsProvided::class, EnsureApiKeyIsValid::class])->group(function () {
    // SAVINGS
    Route::post('/ussd/postDepositRepayment', [SavingsApiController::class, 'deposit'])->name('savings.deposit');
    Route::post('/savings/withdraw', [SavingsApiController::class, 'withdraw'])->name('savings.withdraw');
    Route::get('/savings/balance', [SavingsApiController::class, 'balance'])->name('savings.balance');
    Route::get('/savings/statement', [SavingsApiController::class, 'statement'])->name('savings.statement');
    Route::get('/savings/about', [SavingsApiController::class, 'about'])->name('savings.about');
    Route::post('/savings/account/create', [SavingsApiController::class, 'createAccount'])->name('savings.create');
    Route::post('/savings/applications/reference-code', [\App\Http\Controllers\Api\SavingApplicationReferenceController::class, 'store'])
        ->name('savings-applications.reference.store');
    Route::get('/savings/applications/reference-code/{referenceCode}', [\App\Http\Controllers\Api\SavingApplicationReferenceController::class, 'show'])
        ->name('savings-applications.reference.show');
    // LOANS
    Route::get('/loans/ledger', [LoansApiController::class, 'loanLedger'])->name('loans.ledger');
    Route::get('/loans/balance', [LoansApiController::class, 'loanBalance'])->name('loans.balance');
    Route::get('/loans/schedule', [LoansApiController::class, 'loanSchedule'])->name('loans.schedule');
    Route::get('/loans/products', [LoansApiController::class, 'loanProducts'])->name('loans.products');
    Route::post('/loans/repayment', [LoansApiController::class, 'loanRepayment'])->name('loans.repayment');
    Route::post('/loans/application', [LoansApiController::class, 'loanApplication'])->name('loans.application');
    Route::get('/loans/elegibility', [LoansApiController::class, 'loanElegibility'])->name('loans.elegibility');

    // FOR USSD
    // Eligibility
    Route::post('/ussd/postCreditScore', [UssdChannelApiController::class, 'postCreditScore'])->name('ussd.postCreditScore');
    Route::post('/ussd/postLoanApplication', [UssdChannelApiController::class, 'postLoanApplication'])->name('ussd.postLoanApplication');
    // Loan Application
    Route::post('/ussd/postApprovedLoan', [UssdChannelApiController::class, 'postApprovedLoan'])->name('ussd.postApprovedLoan');
    // Loan Repayment
    Route::post('/ussd/postLoanRepayment', [UssdChannelApiController::class, 'postLoanRepayment'])->name('ussd.postLoanRepayment');
    // Loan Balance
    Route::post('/ussd/getLoanBalance', [UssdChannelApiController::class, 'getLoanBalance'])->name('ussd.getLoanBalance');
    // Loan Mini Statement
    Route::post('/ussd/miniStatement', [UssdChannelApiController::class, 'miniStatement'])->name('ussd.miniStatement');
    Route::post('/ussd/miniStatementDetails', [UssdChannelApiController::class, 'miniStatementDetails'])->name('ussd.miniStatementDetails');
    // Transaction Status check
    Route::post('/ussd/loanStatusCheck', [UssdChannelApiController::class, 'loanStatusCheck'])->name('ussd.loanStatusCheck');
    Route::post('/ussd/restructureLoan', [UssdChannelApiController::class, 'restructureLoan'])->name('ussd.restructureLoan');
    Route::post('/ussd/getRestructureDetails', [UssdChannelApiController::class, 'getRestructureDetails'])->name('ussd.getRestructureDetails');
    Route::post('/ussd/savings/postCustomerPeriod', [UssdChannelApiController::class, 'postCustomerPeriod']);
    Route::post('/ussd/getDepositProduct', [UssdChannelApiController::class, 'getProductDetails']);
    Route::post('/ussd/getCustomerDetails', [UssdChannelApiController::class, 'getCustomerDetails']);
    Route::post('/ussd/optOut', [UssdChannelApiController::class, 'optOut']);
    Route::post('/ussd/getLastTransaction', [UssdChannelApiController::class, 'getLastTransaction']);
    Route::post('/ussd/closeLoan', [UssdChannelApiController::class, 'closeLoan'])->name('ussd.closeLoan');
});
