<div class="card">
    <div class="card-header d-flex justify-content-between">
        <div class="">
            <h5 class="mb-0">GL Statement Break Down Report</h5>
        </div>
        <div class="text-end d-flex justify-content-end">
            <select class="form-select-sm me-2" wire:model.change="accountId">
                <option value="">Choose Account</option>
                @foreach ($accounts as $accountId => $accountName)
                    <option value="{{ $accountId }}">{{ $accountName }}</option>
                @endforeach
            </select>
            <x-date-filter />
            <x-export-buttons :with-excel="true" />
        </div>
    </div>
    <div class="card-body">
        <table id="report-table" class="table table-bordered">
            <thead>
                <tr>
                    <th>ID#</th>
                    <th class="">Account</th>
                    <th class="">Customer</th>
                    <th class="">Phone Number</th>
                    <th class="text-end">Entry Date</th>
                    <th class="text-end">DR</th>
                    <th class="text-end">CR</th>
                    <th class="text-end">Balance</th>
                </tr>
            </thead>
            <tbody>

                @forelse ($records as $record)
                    <tr>
                        <td class="">{{ $record->txn_id }}</td>
                        <td class="">{{ $record->account_name }}</td>
                        <td class="">{{ $record->customer?->name }}</td>
                        <td class="">{{ $record->customer?->Telephone_Number }}</td>
                        <td class="text-end">{{ $record->created_at }}</td>
                        <td class="text-end">{{ number_format($record->debit_amount) }}</td>
                        <td class="text-end">{{ number_format($record->credit_amount) }}</td>
                        <td class="text-end">{{ number_format($record->current_balance) }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7">No records found</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    <div class="card-footer">
        {{ $records->links() }}
    </div>
</div>
