@extends('pdf.layouts')

@section('content')
    <div class="text-center">
        <h2 style="margin-bottom: 5px; margin-top: 0; font-size: 16px">{{ $partnerName }}</h2>
        <h4 style="margin-top: 0; margin-bottom: 4px">Interest Receivable Report</h4>
        <p style="margin-top: 0; font-size: 10px">From: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</p>
    </div>

    <table id="report-table" class="table table-bordered">
        <thead>
            <tr class="table-header">
                <th>Loan #</th>
                <th class="text-start">Customer</th>
                <th class="text-end">Phone Number</th>
                <th class="text-start">Loan Amount</th>
                <th class="text-end">Interest</th>
                <th class="text-end">Interest Receivable</th>
                <th class="text-end">Interest Paid</th>
                <th class="text-end">Accrued Interest</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($records as $record)
                <tr>
                    <td>{{ $record->id }}</td>
                    <td>{{ $record->customer->name }}</td>
                    <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                    <td class="text-end">{{ number_format($record->Credit_Amount) }}</td>
                    <td class="text-end">{{ number_format($record->schedule_sum_interest) }}</td>
                    <td class="text-end">{{ number_format($record->interest_receivable) }}</td>
                    <td class="text-end">{{ number_format($record->interest_paid) }}</td>
                    <td class="text-end">{{ number_format($record->interest_receivable - $record->interest_paid) }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr>
                <th>Totals</th>
                <th class="text-end">{{ $records->count() }}</th>
                <th></th>
                <th class="text-end"><x-money :value="$records->sum('Credit_Amount')" /></th>
                <th class="text-end"><x-money :value="$records->sum('schedule_sum_interest')" /></th>
                <th class="text-end">{{ number_format($irSum = $records->sum('interest_receivable')) }}</th>
                <th class="text-end">{{ number_format($ipSum = $records->sum('interest_paid')) }}</th>
                <th class="text-end">{{ number_format($irSum - $ipSum) }}</th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
