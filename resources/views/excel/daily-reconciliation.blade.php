<table>
    <thead>
        <tr>
            <th colspan="8" style="font-size: 20px; text-align: center;">{{ $partnerName }}</th>
        </tr>
        <tr>
            <th colspan="8" style="font-weight: bold; font-size: 16px; text-align: center; padding: 2px;">Daily Reconciliation Report</th>
        </tr>
        <tr>
            <th colspan="8" style="font-weight: bold; font-size: 12px; text-align: center; padding-top: 2px;">Period: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</th>
        </tr>
        <tr>
            <th colspan="8" style="font-weight: bold; font-size: 12px; text-align: center; padding-top: 2px;">Account Type: {{ $filters['accountType'] }}</th>
        </tr>
        <tr>
            <th style="font-weight: bold; width: 100px">Transaction #</th>
            <th style="font-weight: bold; width: 100px">Transaction Date</th>
            <th style="font-weight: bold; width: 100px;">Customer Name</th>
            <th style="text-align: right; font-weight: bold; width: 100px;">Phone Number</th>
            <th style="font-weight: bold;">Account</th>
            <th style="text-align: right; font-weight: bold; width: 100px;">Debit</th>
            <th style="text-align: right; font-weight: bold; width: 100px;">Credit</th>
            <th style="text-align: right; font-weight: bold; width: 100px;">Balance</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="7">Opening Balance</td>
            <td style="text-align: right">{{ number_format($openingRecord->previous_balance) }}</td>
        </tr>
        @forelse($records as $record)
            <tr>
                <td>{{ $record->id }}</td>
                <td>{{ $record->created_at->toDateTimeString() }}</td>
                <td>{{ $record->customer?->name }}</td>
                <td style="text-align: right;">{{ $record->customer?->Telephone_Number }}</td>
                <td>{{ $record->account_name }}</td>
                <td style="text-align: right">{{ number_format($record->debit_amount) }}</td>
                <td style="text-align: right">{{ number_format($record->credit_amount) }}</td>
                <td style="text-align: right">{{ number_format($record->current_balance) }}</td>
            </tr>
        @empty
            <tr>
                <td colspan="8">No records found</td>
            </tr>
        @endforelse
    </tbody>
    <tfoot>
        <tr>
            <td>Closing Balance</td>
            <td colspan="6"></td>
            <td style="text-align: right">{{ number_format($closingRecord->current_balance) }}</td>
        </tr>
    </tfoot>
</table>

<x-print-footer />
