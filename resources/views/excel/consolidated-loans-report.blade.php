@extends('excel.layouts')

@section('content')
    <table id="report-table" class="table table-bordered">
        <thead>
            <tr>
                <th colspan="16" style="font-size: 20px; text-align: center;">{{ $partner->Institution_Name }}</th>
            </tr>
            <tr>
                <th colspan="16" style="font-weight: bold; font-size: 16px; text-align: center;">Consolidated Loans Report
                </th>
            </tr>
            <tr>
                <th colspan="16" style="font-size: 14px; text-align: center;">As at {{ $filters['endDate'] }}</th>
            </tr>
            <tr>
                <th style="border: 1px solid black; text-align: center" colspan="16">No records found</th>
            </tr>
            <tr>
                <th style="font-weight: bold; border: 1px solid black; width: 80px; background-color: #999999">Loan#</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Customer Name</th>
                <th style="font-weight: bold; border: 1px solid black; width: 150px; background-color: #999999">Phone Number
                </th>
                <th style="font-weight: bold; border: 1px solid black; width: 150px; background-color: #999999">Amount
                    Disbursed</th>
                <th style="font-weight: bold; border: 1px solid black; width: 100px; background-color: #999999">Date
                    Disbursed</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 120px; text-align: right; background-color: #999999">
                    Maturity Date</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 120px; text-align: right; background-color: #999999">
                    Outstanding Balance</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Interest Rate</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Interest Receivable</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Arrears Days</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Arrears Amount</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Principal Overdue</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Interest Overdue</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Penal Interest Overdue</th>
                <th
                    style="font-weight: bold; border: 1px solid black; width: 180px; text-align: right; background-color: #999999">
                    Last Payment</th>
                <th style="font-weight: bold; border: 1px solid black; width: 180px; background-color: #999999">Gender</th>
            </tr>
        </thead>
        <tbody class="">
            @forelse ($records as $record)
                <tr>
                    <td style="">{{ $record->id }}</td>
                    <td style="">{{ $record->customer->name }}</td>
                    <td style="text-align: right; ">{{ $record->customer->Telephone_Number }}</td>
                    <td style="text-align: right; ">
                        {{ number_format($record->Facility_Amount_Granted) }}</td>
                    <td style="">{{ $record->Credit_Account_Date->toDateString() }}</td>
                    <td style="">{{ $record->Maturity_Date->toDateString() }}</td>
                    <td style="">{{ number_format($record->schedule_sum_total_outstanding) }}</td>
                    <td style=" text-align: right;">
                        {{ $record->Annual_Interest_Rate_at_Disbursement }}</td>
                    <td style=" text-align: right;">
                        {{ number_format($record->schedule_sum_interest) }}</td>
                    <td style=" text-align: right;">{{ abs($record->arrears_days) }}</td>
                    <td style=" text-align: right;">{{ number_format($record->total_arrears) }}
                    </td>
                    <td style=" text-align: right;">
                        {{ number_format($record->total_principal_overdue) }}</td>
                    <td style=" text-align: right;">
                        {{ number_format($record->total_interest_overdue) }}</td>
                    <td style=" text-align: right;">
                        {{ number_format($record->penalties_overdue) }}</td>
                    <td style=" text-align: right;">
                        {{ $record->last_payment_date?->toDateString() }}</td>
                    <td style="">{{ $record->customer->Gender }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="16">No records found.</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr>
                <th>Totals</th>
                <th style="text-align:right; font-weight:bold">{{ $records->count() }}</th>
                <th></th>
                <th style="text-align:right; font-weight:bold">
                    {{ number_format($records->sum('Facility_Amount_Granted')) }}</th>
                <th colspan="2"></th>
                <th style="text-align:right; font-weight:bold">
                    {{ number_format($records->sum('schedule_sum_total_outstanding')) }}</th>
                <th></th>
                <th style="text-align:right; font-weight:bold">{{ number_format($records->sum('schedule_sum_interest')) }}
                </th>
                <th></th>
                <th style="text-align:right; font-weight:bold">{{ number_format($records->sum('total_arrears')) }}</th>
                <th style="text-align:right; font-weight:bold">
                    {{ number_format($records->sum('total_principal_overdue')) }}</th>
                <th style="text-align:right; font-weight:bold">{{ number_format($records->sum('total_interest_overdue')) }}
                </th>
                <th style="text-align:right; font-weight:bold">{{ number_format($records->sum('penalties_overdue')) }}</th>
                <th colspan="2"></th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
