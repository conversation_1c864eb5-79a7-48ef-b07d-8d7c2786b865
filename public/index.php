<?php

use Illuminate\Http\Request;

error_reporting(E_ALL);
ini_set('display_errors', '1');
define('LARAVEL_START', microtime(true));

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__ . '/../storage/framework/maintenance.php')) {
  require $maintenance;
}

// Register the Composer autoloader...
require __DIR__ . '/../vendor/autoload.php';

// Bootstrap <PERSON>vel and handle the request...
(require_once __DIR__ . '/../bootstrap/app.php')
  ->handleRequest(Request::capture());
